services:
  # Agent Service
  pathforge_ai_agent_service:
    build:
      context: .
      dockerfile: docker/Dockerfile.service
    ports:
      - "8000:8000"
    environment:
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-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}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
      - OPENROUTER_BASEURL=${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY:-********************************}
      - BRAVE_SEARCH_API_KEY=${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-openrouter}
      # Database Configuration (using SQLite instead of PostgreSQL)
      - DATABASE_TYPE=sqlite
      - DATABASE_URL=sqlite:///./pathforge_integration.db
    volumes:
      - pathforge_data:/app/data
      - .:/app/src
    networks:
      - app_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Service (without PostgreSQL dependency)
  pathforge_ai_backend:
    build:
      context: src/backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # Use Supabase connection instead of local PostgreSQL
      - DATABASE_URL=${SUPABASE_DATABASE_URL:-}
      - SUPABASE_URL=${SUPABASE_URL:-}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-}
      - NODE_ENV=development
      - PORT=8080
    volumes:
      - ./src/backend:/app
      - /app/node_modules
    networks:
      - app_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Streamlit App
  pathforge_ai_streamlit_app:
    build:
      context: .
      dockerfile: docker/Dockerfile.app
    ports:
      - "8501:8501"
    environment:
      - AGENT_URL=http://pathforge_ai_agent_service:8000
      - BACKEND_URL=http://pathforge_ai_backend:8080
    volumes:
      - .:/app/src
    networks:
      - app_network
    depends_on:
      pathforge_ai_agent_service:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend App
  pathforge_ai_frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_AGENT_URL=http://localhost:8000
      - REACT_APP_STREAMLIT_URL=http://localhost:8501
    networks:
      - app_network
    depends_on:
      pathforge_ai_backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  pathforge_data:
    driver: local

networks:
  app_network:
    driver: bridge

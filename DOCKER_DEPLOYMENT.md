# Docker Deployment Guide

This guide provides comprehensive instructions for deploying the CodePluse platform using Docker containers.

## Overview

The CodePluse platform consists of four main services:
- **PathForge AI Agent Service** (Port 8000) - Core AI agent functionality
- **PathForge AI Backend** (Port 8080) - API backend service  
- **PathForge AI Frontend** (Port 3000) - React-based web interface
- **PathForge AI Streamlit App** (Port 8501) - Streamlit dashboard

## Prerequisites

- Docker Engine 20.10.0+
- Docker Compose 2.0.0+
- Git
- 4GB+ available RAM
- 10GB+ available disk space

## Quick Start

### 1. Clone and Setup Environment

```bash
git clone <repository-url>
cd codepluse-platform
cp .env.example .env
```

### 2. Configure Environment Variables

Edit the `.env` file and configure the required variables:

```bash
# Database Configuration (External PostgreSQL recommended for production)
DATABASE_URL=****************************************/database_name
DATABASE_TYPE=postgresql

# Redis Configuration  
REDIS_URL=redis://localhost:6379

# AI Service API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENROUTER_API_KEY=your_openrouter_api_key

# Security
JWT_SECRET=your_secure_jwt_secret_key_here
API_SECRET_KEY=your_secure_api_secret_key_here

# Service URLs (adjust for production)
AGENT_URL=http://localhost:8000
BACKEND_URL=http://localhost:8080
FRONTEND_URL=http://localhost:3000
STREAMLIT_URL=http://localhost:8501
```

### 3. Deploy Services

#### Option A: Full Stack Deployment (Recommended)
```bash
docker-compose -f docker-compose.full.yml up -d
```

#### Option B: Individual Service Deployment
```bash
# Using the main compose file in docker directory
docker-compose -f docker/compose.yaml up -d
```

#### Option C: Backend Only
```bash
docker-compose -f src/backend/docker-compose.yml up -d
```

### 4. Verify Deployment

Check service health:
```bash
# Check running containers
docker-compose -f docker-compose.full.yml ps

# Check logs
docker-compose -f docker-compose.full.yml logs -f

# Test service endpoints
curl http://localhost:8000/health  # AI Agent Service
curl http://localhost:8080/health  # Backend API
curl http://localhost:3000         # Frontend
curl http://localhost:8501         # Streamlit App
```

## Configuration Files

### Docker Compose Files

- **`docker-compose.full.yml`** - Complete stack with all services, health checks, and proper dependencies
- **`docker/compose.yaml`** - Alternative deployment configuration
- **`src/backend/docker-compose.yml`** - Backend-only deployment
- **`docker/docker-compose.swarm.yml`** - Docker Swarm deployment (PostgreSQL removed)

### Environment Configuration

- **`.env.example`** - Template with all configuration options
- **`.env`** - Your local configuration (create from .env.example)
- **`.env.production`** - Production environment variables
- **`.env.staging`** - Staging environment variables

## Service Details

### PathForge AI Agent Service
- **Port**: 8000
- **Health Check**: `/health`
- **Volume**: `pathforge_data:/app/data`
- **Environment**: SQLite by default, PostgreSQL for production

### PathForge AI Backend  
- **Port**: 8080
- **Health Check**: `/health`
- **Technology**: Node.js/Express
- **Database**: Supabase/PostgreSQL

### PathForge AI Frontend
- **Port**: 3000 (mapped from 80)
- **Technology**: React
- **Dependencies**: Backend service

### PathForge AI Streamlit App
- **Port**: 8501
- **Technology**: Streamlit/Python
- **Dependencies**: AI Agent Service

## Production Deployment

### 1. Environment Configuration

For production, ensure you have:

```bash
# Production environment
NODE_ENV=production
ENVIRONMENT=production

# External database
DATABASE_URL=********************************************************/codepluse_prod
DATABASE_TYPE=postgresql

# Secure secrets
JWT_SECRET=very_secure_64_character_random_string_for_production_use_only
API_SECRET_KEY=another_very_secure_random_string_for_api_authentication

# Production URLs
AGENT_URL=https://agent.yourdomain.com
BACKEND_URL=https://api.yourdomain.com
FRONTEND_URL=https://app.yourdomain.com
STREAMLIT_URL=https://dashboard.yourdomain.com
```

### 2. Production Docker Compose

```bash
# Use production-optimized configuration
docker-compose -f docker/compose.prod.yml up -d
```

### 3. SSL/TLS Configuration

Configure reverse proxy (nginx/traefik) for SSL termination:

```nginx
# Example nginx configuration
server {
    listen 443 ssl;
    server_name app.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Docker Swarm Deployment

For cluster deployment:

```bash
# Initialize swarm (if not already done)
docker swarm init

# Deploy stack
docker stack deploy -c docker/docker-compose.swarm.yml codepluse

# Check stack status
docker stack services codepluse
```

## Monitoring and Maintenance

### Health Checks

All services include health checks:
- Interval: 30 seconds
- Timeout: 10 seconds  
- Retries: 3

### Logs

```bash
# View all logs
docker-compose -f docker-compose.full.yml logs -f

# View specific service logs
docker-compose -f docker-compose.full.yml logs -f pathforge_ai_agent_service

# Follow logs with timestamps
docker-compose -f docker-compose.full.yml logs -f -t
```

### Backup and Restore

```bash
# Backup volumes
docker run --rm -v codepluse-platform_pathforge_data:/data -v $(pwd):/backup ubuntu tar czf /backup/pathforge_data_backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v codepluse-platform_pathforge_data:/data -v $(pwd):/backup ubuntu tar xzf /backup/pathforge_data_backup.tar.gz -C /data
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 8000, 8080, 8501 are available
2. **Memory issues**: Increase Docker memory allocation to 4GB+
3. **Build failures**: Check Dockerfile contexts and dependencies

### Service Connectivity

```bash
# Test internal network connectivity
docker exec -it <container_name> curl http://pathforge_ai_backend:8080/health

# Check container networks
docker network ls
docker network inspect codepluse-platform_app_network
```

### Performance Monitoring

```bash
# Monitor resource usage
docker stats

# Container inspection
docker inspect <container_name>
```

## Development Mode

For development with hot reloading:

```bash
# Use development configuration with volume mounts
docker-compose -f docker/compose.yaml up -d

# Or run individual services
docker-compose -f docker-compose.full.yml up pathforge_ai_backend
```

## Scaling Services

```bash
# Scale specific services
docker-compose -f docker-compose.full.yml up -d --scale pathforge_ai_agent_service=3

# For swarm mode
docker service scale codepluse_pathforge_ai_agent_service=3
```

## Security Considerations

1. **API Keys**: Store in environment variables, never in code
2. **Database**: Use external managed database for production
3. **Secrets**: Use Docker secrets in swarm mode
4. **Network**: Configure firewall rules for exposed ports
5. **Updates**: Regularly update base images and dependencies

## Support

For issues and questions:
- Check logs: `docker-compose logs -f`
- Verify configuration: `docker-compose config`
- Health checks: `curl http://localhost:<port>/health`
- Documentation: See individual service README files

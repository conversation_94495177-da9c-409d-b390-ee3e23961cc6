# Docker Setup Guide

Complete Docker setup and deployment guide for the CodePluse platform.

## Quick Start

1. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Deploy Platform**
   ```bash
   ./scripts/deploy.sh
   ```

3. **Check Health**
   ```bash
   ./scripts/health-check.sh
   ```

## Available Services

- **Frontend**: http://localhost:3000 (React Web App)
- **Backend API**: http://localhost:8080 (Node.js/Express)
- **AI Agent**: http://localhost:8000 (Python/FastAPI)
- **Streamlit App**: http://localhost:8501 (Python/Streamlit)

## Docker Configurations

### 1. Full Stack (`docker-compose.full.yml`)
Complete deployment with all services, health checks, and proper dependencies.

```bash
docker-compose -f docker-compose.full.yml up -d
```

### 2. Basic Stack (`docker/compose.yaml`)
Simplified deployment for development.

```bash
docker-compose -f docker/compose.yaml up -d
```

### 3. Monitoring Stack (`docker-compose.monitoring.yml`)
Includes Prometheus, Grafana, and Traefik for production monitoring.

```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

### 4. Swarm Deployment (`docker/docker-compose.swarm.yml`)
For Docker Swarm cluster deployment (PostgreSQL removed for external DB).

```bash
docker stack deploy -c docker/docker-compose.swarm.yml codepluse
```

## Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Database (External PostgreSQL recommended for production)
DATABASE_URL=********************************/db
DATABASE_TYPE=postgresql

# Redis Cache
REDIS_URL=redis://localhost:6379

# AI Services
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
OPENROUTER_API_KEY=your_openrouter_key

# Security
JWT_SECRET=your_secure_jwt_secret
API_SECRET_KEY=your_secure_api_secret

# Service URLs
AGENT_URL=http://localhost:8000
BACKEND_URL=http://localhost:8080
FRONTEND_URL=http://localhost:3000
STREAMLIT_URL=http://localhost:8501
```

## Deployment Scripts

### Deploy Script (`scripts/deploy.sh`)
Comprehensive deployment automation with backup, health checks, and verification.

```bash
# Full deployment
./scripts/deploy.sh

# Available commands
./scripts/deploy.sh deploy    # Full deployment (default)
./scripts/deploy.sh build     # Build services only
./scripts/deploy.sh start     # Start existing services
./scripts/deploy.sh stop      # Stop running services
./scripts/deploy.sh restart   # Restart services
./scripts/deploy.sh status    # Show deployment status
./scripts/deploy.sh logs      # Show service logs
./scripts/deploy.sh cleanup   # Clean up Docker resources
./scripts/deploy.sh backup    # Create backup only

# Options
./scripts/deploy.sh --no-backup deploy    # Skip backup creation
./scripts/deploy.sh --aggressive cleanup  # Aggressive cleanup
```

### Health Check Script (`scripts/health-check.sh`)
Comprehensive health monitoring and diagnostics.

```bash
# Full health check
./scripts/health-check.sh

# Specific checks
./scripts/health-check.sh quick       # Quick check
./scripts/health-check.sh services    # Service endpoints only
./scripts/health-check.sh resources   # Resource usage only
./scripts/health-check.sh network     # Network connectivity
./scripts/health-check.sh logs        # Log analysis
./scripts/health-check.sh performance # Performance tests

# Generate report
./scripts/health-check.sh --report
```

## Production Deployment

### 1. Prerequisites
- Docker Engine 20.10.0+
- Docker Compose 2.0.0+
- 4GB+ RAM
- 10GB+ disk space
- External PostgreSQL database
- SSL certificates (for HTTPS)

### 2. Environment Setup
```bash
# Production environment variables
NODE_ENV=production
ENVIRONMENT=production

# External database
DATABASE_URL=***********************************************/codepluse_prod

# Secure secrets
JWT_SECRET=very_secure_64_character_random_string
API_SECRET_KEY=another_very_secure_random_string

# Production URLs
AGENT_URL=https://agent.yourdomain.com
BACKEND_URL=https://api.yourdomain.com
FRONTEND_URL=https://app.yourdomain.com
STREAMLIT_URL=https://dashboard.yourdomain.com
```

### 3. SSL/Reverse Proxy
Use the monitoring stack with Traefik for automatic SSL:

```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

Or configure nginx manually:
```nginx
server {
    listen 443 ssl;
    server_name app.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring and Logging

### Built-in Monitoring
The monitoring stack includes:
- **Prometheus** (http://localhost:9090) - Metrics collection
- **Grafana** (http://localhost:3001) - Visualization (admin/admin123)
- **Traefik** (http://localhost:8090) - Load balancer and SSL

### Log Management
All services use structured logging with rotation:
- Max size: 100MB per file
- Max files: 5 files per service
- Format: JSON

View logs:
```bash
# All services
docker-compose -f docker-compose.full.yml logs -f

# Specific service
docker-compose -f docker-compose.full.yml logs -f pathforge_ai_agent_service

# With timestamps
docker-compose -f docker-compose.full.yml logs -f -t
```

## Backup and Recovery

### Automated Backups
The deploy script automatically creates backups:
- Environment configuration
- Docker volumes
- Timestamp-based backup directories

### Manual Backup
```bash
# Create backup
./scripts/deploy.sh backup

# Backup specific volume
docker run --rm \
  -v codepluse-platform_pathforge_data:/data \
  -v $(pwd)/backup:/backup \
  ubuntu tar czf /backup/pathforge_data.tar.gz -C /data .
```

### Restore
```bash
# Restore volume
docker run --rm \
  -v codepluse-platform_pathforge_data:/data \
  -v $(pwd)/backup:/backup \
  ubuntu tar xzf /backup/pathforge_data.tar.gz -C /data
```

## Development Mode

For development with hot reloading:

```bash
# Use development configuration
docker-compose -f docker/compose.yaml up -d

# Or run services individually
docker-compose -f docker-compose.full.yml up pathforge_ai_backend
```

### Development Features
- Volume mounts for source code
- Hot reloading enabled
- Development environment variables
- Debug modes enabled

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   lsof -i :3000,8000,8080,8501
   ```

2. **Memory Issues**
   ```bash
   # Increase Docker memory allocation
   # Check current usage
   docker stats
   ```

3. **Build Failures**
   ```bash
   # Clean build
   docker-compose -f docker-compose.full.yml build --no-cache
   ```

4. **Service Connectivity**
   ```bash
   # Test internal connectivity
   docker exec <container> curl http://pathforge_ai_backend:8080/health
   ```

### Debugging Commands

```bash
# Container inspection
docker inspect <container_name>

# Network inspection
docker network ls
docker network inspect codepluse-platform_app_network

# Volume inspection
docker volume ls
docker volume inspect codepluse-platform_pathforge_data

# Resource monitoring
docker stats

# System events
docker system events
```

## Scaling

### Horizontal Scaling
```bash
# Scale specific services
docker-compose -f docker-compose.full.yml up -d --scale pathforge_ai_agent_service=3

# For swarm mode
docker service scale codepluse_pathforge_ai_agent_service=3
```

### Load Balancing
Use Traefik (included in monitoring stack) for automatic load balancing and service discovery.

## Security Best Practices

1. **API Keys**: Store in environment variables, never in code
2. **Database**: Use external managed database for production
3. **Secrets**: Use Docker secrets in swarm mode
4. **Network**: Configure firewall rules for exposed ports
5. **Updates**: Regularly update base images and dependencies
6. **SSL**: Always use HTTPS in production
7. **Monitoring**: Enable security monitoring and alerting

## Additional Resources

- **Deployment Guide**: [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md)
- **Health Checks**: Run `./scripts/health-check.sh --help`
- **Monitoring**: Access Grafana at http://localhost:3001
- **API Documentation**: Backend API docs at http://localhost:8080/docs

## Support

For issues and questions:
1. Check service health: `./scripts/health-check.sh`
2. View logs: `docker-compose logs -f`
3. Verify configuration: `docker-compose config`
4. Check documentation in `docs/` directory
4. **Frontend App** (port 3000) - React frontend

## Yêu cầu trước khi chạy:

### 1. Cài đặt Docker Desktop
Đảm bảo Docker Desktop đã được cài đặt và đang chạy

### 2. Cấu hình Environment Variables
```bash
# Copy file env example
cp .env.example .env

# Chỉnh sửa file .env với thông tin Supabase của bạn
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_DATABASE_URL=*****************************************************/postgres
```

## Cách chạy:

### Option 1: Sử dụng compose.yaml (Recommended)
```bash
# Chạy tất cả services
docker-compose up --build

# Hoặc chạy ở background
docker-compose up -d --build

# Xem logs
docker-compose logs -f

# Dừng services
docker-compose down
```

### Option 2: Sử dụng docker-compose.full.yml (với healthchecks)
```bash
# Chạy với file full
docker-compose -f docker-compose.full.yml up --build

# Background mode
docker-compose -f docker-compose.full.yml up -d --build

# Dừng
docker-compose -f docker-compose.full.yml down
```

## Access URLs sau khi chạy:
- **Agent Service**: http://localhost:8000
- **Backend API**: http://localhost:8080  
- **Streamlit App**: http://localhost:8501
- **Frontend**: http://localhost:3000

## Troubleshooting:

### Nếu services không start được:
```bash
# Xem logs chi tiết
docker-compose logs [service_name]

# Ví dụ:
docker-compose logs pathforge_ai_agent_service
docker-compose logs pathforge_ai_backend
```

### Reset hoàn toàn:
```bash
# Dừng và xóa tất cả
docker-compose down -v

# Xóa images để rebuild
docker-compose down --rmi all

# Rebuild từ đầu
docker-compose up --build --force-recreate
```

### Kiểm tra status:
```bash
# Xem containers đang chạy
docker-compose ps

# Xem resource usage
docker stats
```

## Notes:
- Project đã được cấu hình để sử dụng Supabase thay vì PostgreSQL local
- Tất cả services sẽ tự động restart nếu gặp lỗi (unless-stopped)
- Data được persist qua Docker volumes
- Network isolation giữa các services

name: PathForge AI - Docker Build

on:
  workflow_call:
    inputs:
      push:
        description: 'Push images to registry'
        required: false
        type: boolean
        default: false
      registry:
        description: 'Docker registry'
        required: false
        type: string
        default: ''
      tag-prefix:
        description: 'Tag prefix for images'
        required: false
        type: string
        default: 'latest'
      report-sizes:
        description: 'Report Docker image sizes in summary'
        required: false
        type: boolean
        default: false
      build-agent-service:
        description: 'Build agent service image'
        required: false
        type: boolean
        default: true
      build-backend-service:
        description: 'Build backend service image'
        required: false
        type: boolean
        default: true
      build-streamlit-app:
        description: 'Build streamlit app image'
        required: false
        type: boolean
        default: true
      build-frontend:
        description: 'Build frontend image'
        required: false
        type: boolean
        default: true
    secrets:
      HEROKU_API_KEY:
        required: false
      HEROKU_EMAIL:
        required: false
      # Keep backward compatibility for other Docker registries
      DOCKER_USERNAME:
        required: false
      DOCKER_PASSWORD:
        required: false

jobs:
  # Generate matrix based on input parameters
  generate-matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
    - name: Generate build matrix
      id: set-matrix
      run: |
        services=()
        if [ "${{ inputs.build-agent-service }}" = "true" ]; then
          services+=("agent_service")
        fi
        if [ "${{ inputs.build-streamlit-app }}" = "true" ]; then
          services+=("streamlit_app")
        fi
        if [ "${{ inputs.build-frontend }}" = "true" ]; then
          services+=("frontend")
        fi
        if [ "${{ inputs.build-backend-service }}" = "true" ]; then
          services+=("backend")
        fi

        # Convert array to JSON (compact format)
        matrix_json=$(printf '%s\n' "${services[@]}" | jq -R . | jq -s -c .)
        echo "matrix={\"service\":$matrix_json}" >> $GITHUB_OUTPUT
        echo "Generated matrix: {\"service\":$matrix_json}"

  docker-build:
    runs-on: ubuntu-latest
    needs: generate-matrix
    if: needs.generate-matrix.outputs.matrix != '{"service":[]}'
    strategy:
      matrix: ${{ fromJson(needs.generate-matrix.outputs.matrix) }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: ${{ inputs.push && inputs.registry }}
      uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ startsWith(inputs.registry, 'registry.heroku.com') && secrets.HEROKU_EMAIL || secrets.DOCKER_USERNAME }}
        password: ${{ startsWith(inputs.registry, 'registry.heroku.com') && secrets.HEROKU_API_KEY || secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      if: ${{ inputs.push && inputs.registry }}
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          ${{ startsWith(inputs.registry, 'registry.heroku.com') && 
              format('{0}/{1}/{2}', inputs.registry, 'pathforge-ai',
                matrix.service == 'frontend' && 'web' ||
                matrix.service == 'agent_service' && 'agent' ||
                matrix.service == 'streamlit_app' && 'streamlit' ||
                matrix.service == 'backend' && 'api' ||
                matrix.service) ||
              format('{0}/pathforge-ai/{1}', inputs.registry, matrix.service) }}
        tags: |
          type=raw,value=${{ inputs.tag-prefix }}
          type=raw,value=latest

    - name: Setup dockerignore for build
      run: |
        if [ "${{ matrix.service }}" = "agent_service" ]; then
          cp docker/.dockerignore.service .dockerignore
        elif [ "${{ matrix.service }}" = "frontend" ]; then
          cp docker/.dockerignore.frontend .dockerignore
        else
          cp docker/.dockerignore.app .dockerignore
        fi

    - name: Build and push Docker image
      uses: docker/build-push-action@v6
      with:
        context: ${{ matrix.service == 'backend' && 'src/backend' || '.' }}
        file: |
          ${{ matrix.service == 'backend' && 'src/backend/Dockerfile' ||
             (matrix.service == 'agent_service' && 'docker/Dockerfile.service') ||
             (matrix.service == 'frontend' && 'docker/Dockerfile.frontend') ||
             'docker/Dockerfile.app' }}
        push: ${{ inputs.push }}
        tags: ${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}
        labels: ${{ inputs.push && inputs.registry && steps.meta.outputs.labels || '' }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Report image size and optimization info
      if: ${{ inputs.report-sizes }}
      run: |
        IMAGE_TAG="${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}"
        IMAGE_SIZE=$(docker images $IMAGE_TAG --format 'table {{.Size}}' | tail -n 1)
        IMAGE_SIZE_BYTES=$(docker images $IMAGE_TAG --format 'table {{.Size}}' | tail -n 1 | sed 's/[^0-9.]//g')

        # Get image details for optimization info
        IMAGE_LAYERS=$(docker history $IMAGE_TAG --no-trunc --format "table {{.Size}}" | grep -v SIZE | wc -l)

        # Create or append to size summary with optimization details
        if [ "${{ matrix.service }}" = "agent_service" ]; then
          echo "## 📦 Docker Image Sizes & Optimization" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Service | Size | Layers | Optimization |" >> $GITHUB_STEP_SUMMARY
          echo "|---------|------|--------|-------------|" >> $GITHUB_STEP_SUMMARY
          echo "| Agent Service | $IMAGE_SIZE | $IMAGE_LAYERS | Multi-stage Alpine build |" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ matrix.service }}" = "streamlit_app" ]; then
          echo "| Streamlit App | $IMAGE_SIZE | $IMAGE_LAYERS | Optimized Alpine + minimal deps |" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ matrix.service }}" = "backend" ]; then
          echo "| Backend Service | $IMAGE_SIZE | $IMAGE_LAYERS | Node.js Alpine with Prisma |" >> $GITHUB_STEP_SUMMARY # Example description
        else
          echo "| Frontend | $IMAGE_SIZE | $IMAGE_LAYERS | Multi-stage Nginx + Node Alpine |" >> $GITHUB_STEP_SUMMARY
        fi

    - name: Add Docker build summary
      run: |
        if [ "${{ inputs.push }}" = "true" ] && [ -n "${{ inputs.registry }}" ]; then
          FULL_IMAGE_NAME="${{ inputs.registry }}/pathforge-ai/${{ matrix.service }}:${{ inputs.tag-prefix }}"
          echo "## 🐳 Docker Build Summary - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Full Image Name:** \`$FULL_IMAGE_NAME\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Registry:** ${{ inputs.registry }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Service:** ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tag:** ${{ inputs.tag-prefix }}" >> $GITHUB_STEP_SUMMARY
          if [ "${{ matrix.service }}" = "streamlit_app" ]; then
            echo "- **Optimization:** Multi-stage Alpine build with minimal dependencies" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ matrix.service }}" = "frontend" ]; then
            echo "- **Optimization:** Multi-stage Node Alpine + Nginx with static build" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ matrix.service }}" = "backend" ]; then
            echo "- **Optimization:** Node.js Alpine build with Prisma client generation" >> $GITHUB_STEP_SUMMARY # Example
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
        else
          echo "## 🐳 Docker Build Summary - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Local Image:** \`${{ matrix.service }}:latest\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Service:** ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tag:** latest" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** Built locally (not pushed)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
        fi
